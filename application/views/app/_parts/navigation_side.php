<aside class="main-sidebar sidebar-dark-primary elevation-pro">
	<!-- Sidebar -->
	<div class="sidebar p-0">
		<div class="text-center p-2" style="background-image: linear-gradient(135deg, #24ae61, #12623e)!important;height: 56px!important;">
			<a href="<?=base_url('app/dashboard/index')?>">
				<img src="<?=base_url('assets/logo/logo.png')?>" style="max-height: 40px;width: auto;max-width: 100%" alt="">
			</a>
		</div>

		<?php
		$controller_name = $this->router->fetch_class();
		$method_name = $this->router->fetch_method();
        $user = $this->db->get_where('users', ['id' => get_user_id()])->row();
        if(is_file($user->photo)){
            $profile_image = base_url($user->photo);
        }else{
            $profile_image = base_url('uploads/default.jpg');
        }
		?>
		<!-- Sidebar Menu -->
		<nav class="mt-2 p-2">
			<div style="padding: 15px 10px; background: linear-gradient(to right, #f8f9fa, #e9ecef); border-radius: 10px; margin-bottom: 15px;">
				<div style="display: flex; align-items: center;">
					<!-- User Image with Status Indicator -->
					<div style="position: relative; margin-right: 12px;">
						<img src="<?=$profile_image?>" style="width: 70px; height: 70px; border-radius: 50%; object-fit: cover; border: 3px solid #fff; box-shadow: 0 3px 8px rgba(0,0,0,0.15);">
						<div style="position: absolute; bottom: 3px; right: 3px; width: 12px; height: 12px; background-color: #28a745; border-radius: 50%; border: 2px solid #fff;"></div>
					</div>

					<!-- User Info -->
					<div style="flex: 1;">
						<div style="font-weight: 600; font-size: 16px; color: #2c3e50; margin-bottom: 4px; line-height: 1.2;"><?=strtoupper($this->session->userdata('user_name'))?></div>
						<div style="display: flex; align-items: center; font-size: 13px; color: #495057;">
							<i class="fas fa-phone" style="margin-right: 5px; font-size: 12px; color: #6c757d;"></i>
							<span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><?=$user->phone?></span>
						</div>
					</div>
				</div>
			</div>
			<hr>

			<ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item <?=$controller_name == 'dashboard' ? 'menu-open' : ''?>" style="margin-top: 0px;">
					<a href="<?= base_url('app/dashboard/index'); ?>" class="nav-link <?=$controller_name == 'dashboard' ? 'active' : ''?>"
					   style="background-color: #eaf1fa;color: #23384E;">
						<i class="nav-icon bi bi-speedometer2"></i>
						<p>
							DASHBOARD
						</p>
					</a>
				</li>
                <?php
                if (has_permission('tasks/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/tasks/index/?task_status=pending'); ?>" class="nav-link <?=$controller_name == 'tasks' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-view-list"></i>
                            <p>
                                TASKS
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('team_members/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/team_members/index/'); ?>" class="nav-link <?=$controller_name == 'team_members' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-people"></i>
                            <p>
                                TEAM MEMBERS
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('work_report')) {
                    ?>
                    <li class="nav-item has-treeview <?=$controller_name == 'work_report' ? 'menu-open' : ''?>" style="margin-top: 0px;">
                        <a href="#" class="nav-link <?=$controller_name == 'work_report' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-view-list"></i>
                            <p>
                                WORK REPORT
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="<?= base_url('app/work_report/overview_report/'); ?>" class="nav-link <?=$controller_name == 'work_report' && $method_name == 'overview_report' ? 'active' : ''?>">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Daily Work Report</p>
                                </a>
                            </li>
                            <?php if (is_project_manager() || is_hr() || is_admin()) { ?>
                            <li class="nav-item">
                                <a href="<?= base_url('app/work_report/employee_status_report/'); ?>" class="nav-link <?=$controller_name == 'work_report' && $method_name == 'employee_status_report' ? 'active' : ''?>">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Monthly Work Status</p>
                                </a>
                            </li>
                            <?php } ?>
                        </ul>
                    </li>
                    <?php
                }
                if (has_permission('work_report')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/work_assign/index/'); ?>" class="nav-link <?=$controller_name == 'work_assign' && $method_name == 'index' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-view-list"></i>
                            <p>
                                WORK ASSIGN
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('project_assign/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/project_assign/daily_assign/'); ?>" class="nav-link <?=$controller_name == 'project_assign' && $method_name == 'daily_assign' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-calendar2-check"></i>
                            <p>
                                DAILY PROJECT ASSIGN
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('tasks_report/task_overview')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/tasks_report/task_overview/'); ?>" class="nav-link <?=$controller_name == 'work_assign' && $method_name == 'index' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-view-list"></i>
                            <p>
                                TASK OVERVIEW
                            </p>
                        </a>
                    </li>
                    <?php
                }

                if (has_permission('projects/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/projects/new_projects'); ?>" class="nav-link <?=$controller_name == 'projects' && $method_name == 'new_projects' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-menu-button-wide-fill"></i>
                            <p>
                                PROJECTS <span class="badge badge-success">NEW</span>
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('project_schedule/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/projects/new_projects'); ?>" class="nav-link <?=$controller_name == 'project_schedule' && $method_name == 'index' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-menu-button-wide-fill"></i>
                            <p>
                                PROJECTS <span class="badge badge-info">SCHEDULE</span>
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('documents/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/documents/index'); ?>" class="nav-link <?=$controller_name == 'documents' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-menu-button-wide-fill"></i>
                            <p>
                                DOCUMENTS
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('projects/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/projects/index'); ?>" class="nav-link <?=$controller_name == 'projects' && $method_name == 'index' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-menu-button-wide-fill"></i>
                            <p>
                                PROJECTS
                            </p>
                        </a>
                    </li>
                    <?php
                }

                if (has_permission('clients/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/clients/index'); ?>" class="nav-link <?=$controller_name == 'clients' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-person-check-fill"></i>
                            <p>
                                CLIENTS
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('users/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/users/index'); ?>" class="nav-link <?=$controller_name == 'users' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-people-fill"></i>
                            <p>
                                USERS
                            </p>
                        </a>
                    </li>
                    <?php
                }
				if (has_permission('teams/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/teams/index'); ?>" class="nav-link <?=$controller_name == 'teams' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-people-fill"></i>
                            <p>
                                TEAMS
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('todo_category/index')) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/todo_category/index'); ?>" class="nav-link <?=$controller_name == 'todo_category' ? 'active' : ''?>">
                            <i class="nav-icon bi bi-tags-fill"></i>
                            <p>
                                TODO CATEGORY
                            </p>
                        </a>
                    </li>
                    <?php
                }
                if (has_permission('time_log/daily_log') || get_user_id() == 57) {
                    ?>
                    <li class="nav-item" style="margin-top: 0px;">
                        <a href="<?= base_url('app/time_log/daily_log'); ?>" class="nav-link <?=$controller_name == 'time_log' ? 'active' : ''?>">
                            <i class="bi bi-calendar-check"></i>
                            <p>
                                ATTENDANCE
                            </p>
                        </a>
                    </li>
                    <?php
                }
                ?>

				<?php
				if (has_permission('reports') || get_user_id() == 57) {
					?>
					<ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu">
						<li class="nav-item has-treeview ">
							<a href="#" class="nav-link nav-parent">
								<i class="nav-icon fas fa-chart-bar"></i>
								<p>
									Reports
									<i class="right fas fa-angle-left"></i>
								</p>
							</a>
							<ul class="nav nav-treeview">

								<?php
								if (has_permission('time_log_report/daily_report')) {
									?>
									<li class="nav-item">
										<a href="<?= base_url('app/time_log_report/overview_report/?start_date=2025-05-01&end_date=2025-05-31&working_days=25'); ?>" class="nav-link">
											<i class="nav-icon fas fa-table"></i>
											<p>Performance</p>
										</a>
									</li>
									<?php
								}
								?>



                                <?php
								if (has_permission('pending_report_employee/index')) {
									?>
									<li class="nav-item">
										<a href="<?= base_url('app/pending_report_employee/index'); ?>" class="nav-link">
											<i class="nav-icon fas fa-table"></i>
											<p>Pending - Employee</p>
										</a>
									</li>
									<?php
								}
								?>

                                <?php
								if (has_permission('pending_report_project/index')) {
									?>
									<li class="nav-item">
										<a href="<?= base_url('app/pending_report_project/index'); ?>" class="nav-link">
											<i class="nav-icon fas fa-table"></i>
											<p>Pending - Project</p>
										</a>
									</li>
									<?php
								}
								?>

                                <?php
								if (has_permission('time_log_report/daily_report')) {
									?>
									<li class="nav-item">
										<a href="<?= base_url('app/time_log_report/daily_report'); ?>" class="nav-link">
											<i class="nav-icon fas fa-calendar"></i>
											<p>Daily Attendance Report</p>
										</a>
									</li>
									<?php
								}
								?>
                                <?php
								if (has_permission('attendance_report/attendance_status_report')) {
									?>
									<li class="nav-item">
										<a href="<?= base_url('app/attendance_report/attendance_status_report'); ?>" class="nav-link">
											<i class="nav-icon fas fa-calendar"></i>
											<p>Attendance Report</p>
										</a>
									</li>
									<?php
								}
								?>

                                <?php
								if (has_permission('project_report/project_employee')) {
									?>
									<li class="nav-item">
										<a href="<?= base_url('app/project_report/project_employee'); ?>" class="nav-link">
											<i class="nav-icon fas fa-project-diagram"></i>
											<p>Project Employee Report</p>
										</a>
									</li>
									<?php
								}
								?>


							</ul>

						</li>
					</ul>
					<?php
				}
				?>

				<?php
				if (has_permission('settings')) {
					$settings = ['project_phases/index'];
					$tree_view = in_array($page_name, $settings) ? 'menu-open' : '';
					?>
					<ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu">
						<li class="nav-item has-treeview <?=$tree_view?>">
							<a href="#" class="nav-link ">
								<i class="nav-icon bi bi-gear-fill"></i>
								<p>
									Settings
									<i class="right fas fa-angle-left"></i>
								</p>
							</a>
							<ul class="nav nav-treeview">
								<?php
								if (has_permission('project_phases/index')) {
									?>
									<li class="nav-item">
										<a href="<?= base_url('app/project_phases/index'); ?>" class="nav-link <?=$page_name == 'project_phases/index' ? 'active' : ''?>">
											<i class="nav-icon bi bi-folder"></i>
											<p>Project Phases</p>
										</a>
									</li>
									<?php
								}
								?>

							</ul>

						</li>
					</ul>

					<?php
				}
				?>
                <div class="d-block p-1">
                    <span class="d-block p-1" style="padding-left:10px!important;font-size: 14px;background-color: #e4edf8">
                        TASKS
                    </span>
                    <div class="row text-center">
                        <div class="col-6 p-2 m-0">
                            <div class="p-1" style="background-color: rgb(253,234,243);border-radius: 5px; color: #800a43">
                                <div style="font-weight: 900!important;font-size: 23px;margin-bottom: -10px!important;">
                                    <?=$tasks_pending_count ?? 0?>
                                </div>
                                <span style="font-size: 11px;">PENDING</span>
                            </div>
                        </div>
                        <div class="col-6 p-2 m-0">
                            <div class="p-1" style="background-color: rgb(255,238,239);border-radius: 5px; color: #ec0505">
                                <div style="font-weight: 900!important;font-size: 23px;margin-bottom: -10px!important;">
                                    <?=$tasks_pending_due_count ?? 0?>
                                </div>
                                <span style="font-size: 11px">EXPIRED</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-block p-1">
                    <span class="d-block p-1" style="padding-left:10px!important;font-size: 14px;background-color: #e4edf8">
                        TODO
                    </span>
                    <div class="row text-center">
                        <div class="col-6 p-2 m-0">
                            <div class="p-1" style="background-color: rgb(253,234,243);border-radius: 5px; color: #800a43">
                                <div style="font-weight: 900!important;font-size: 23px;margin-bottom: -10px!important;">
                                    <?=$todo_pending_count ?? 0?>
                                </div>
                                <span style="font-size: 11px;">PENDING</span>
                            </div>
                        </div>
                        <div class="col-6 p-2 m-0">
                            <div class="p-1" style="background-color: rgb(255,238,239);border-radius: 5px; color: #ec0505">
                                <div style="font-weight: 900!important;font-size: 23px;margin-bottom: -10px!important;">
                                    <?=$todo_pending_due_count ?? 0?>
                                </div>
                                <span style="font-size: 11px">EXPIRED</span>
                            </div>
                        </div>
                    </div>
                </div>
		</nav>
		<!-- /.sidebar-menu -->
	</div>
	<!-- /.sidebar -->
</aside>
